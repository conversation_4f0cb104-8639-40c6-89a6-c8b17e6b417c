# 摄像头编号自动完成功能实现说明

## 功能概述
在驱鼠记录页面（SensingEvent）的摄像头编号输入框中实现了自动完成功能，当用户输入摄像头编号时，系统会自动调用 `getCameraLikeCameraNo` 接口获取匹配的摄像头编号列表，并在下拉框中显示供用户选择。

## 实现的功能特性

### 1. 自动搜索
- 当用户在摄像头编号输入框中输入内容时，系统会自动触发搜索
- 使用了 300ms 的防抖机制，避免频繁调用接口
- 只有当输入内容不为空时才会调用接口

### 2. 下拉选择
- 搜索结果以下拉列表的形式展示
- 用户可以点击下拉列表中的任意选项
- 点击后会自动填充到输入框中

### 3. 样式设计
- 下拉列表样式与图片中的样式保持一致
- 输入框宽度设置为 200px，保持界面美观
- 使用 Ant Design 的 AutoComplete 组件，确保用户体验一致

## 技术实现细节

### 1. 组件导入
```javascript
import { AutoComplete } from "antd";
import { debounce } from "lodash";
import { getCameraLikeCameraNo } from "../../service/deviceCamera";
```

### 2. 状态管理
```javascript
const [cameraOptions, setCameraOptions] = useState([]); // 摄像头编号自动完成选项
```

### 3. 搜索函数
```javascript
const handleCameraNoSearch = debounce(async (value) => {
  if (value && value.trim()) {
    try {
      const response = await getCameraLikeCameraNo(value.trim());
      if (response && Array.isArray(response)) {
        setCameraOptions(
          response.map((cameraNo) => ({
            value: cameraNo,
            label: cameraNo,
          }))
        );
      } else {
        setCameraOptions([]);
      }
    } catch (error) {
      console.error("搜索摄像头编号失败:", error);
      setCameraOptions([]);
    }
  } else {
    setCameraOptions([]);
  }
}, 300);
```

### 4. 组件使用
```javascript
<Form.Item name="cameraNo" label="摄像头编号：">
  <AutoComplete
    placeholder="请输入摄像头编号"
    options={cameraOptions}
    onSearch={handleCameraNoSearch}
    onSelect={(value) => {
      form.setFieldsValue({ cameraNo: value });
    }}
    filterOption={false}
    style={{ width: 200 }}
  />
</Form.Item>
```

## 接口说明

### getCameraLikeCameraNo 接口
- **路径**: `/api/v1/device-service/cameras/getCameraLikeCameraNo`
- **方法**: GET
- **参数**: `cameraNo` (string) - 摄像头编号关键字
- **返回**: 字符串数组，如 `["3TPC45291005N6", "3TPC45291005N61", "3TPC45291005N62", ...]`

## 用户使用流程

1. 用户在摄像头编号输入框中开始输入
2. 系统自动调用接口搜索匹配的摄像头编号
3. 搜索结果显示在下拉列表中
4. 用户点击选择所需的摄像头编号
5. 选中的编号自动填充到输入框中

## 性能优化

1. **防抖处理**: 使用 300ms 防抖，减少不必要的接口调用
2. **输入验证**: 只有当输入内容不为空时才调用接口
3. **错误处理**: 包含完整的错误处理机制，确保接口异常时不影响用户体验
4. **状态清理**: 当输入为空时自动清空选项列表

## 兼容性说明

- 使用 Ant Design 4.x+ 的 AutoComplete 组件
- 兼容现有的表单验证和提交逻辑
- 不影响原有的搜索和重置功能
