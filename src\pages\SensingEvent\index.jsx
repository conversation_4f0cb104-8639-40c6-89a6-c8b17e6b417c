import { useCallback, useEffect, useState } from "react";
import "./styles.css";
import {
  getSensingEventList,
  deleteSensingEvent,
  getSensingEvent,
  getDevicesByCameraId,
} from "../../service/sensingEvent";
import {
  getDeviceGroupsByLocation,
  getCustomerGroupsTree,
} from "../../service/deviceGroups";
import {
  Table,
  DatePicker,
  Button,
  Form,
  Input,
  Select,
  Tooltip,
  Space,
  Modal,
  Descriptions,
  Tree,
  Card,
  Row,
  Col,
  Tabs,
  Layout,
  List,
  Drawer,
  Tag,
  Divider,
  AutoComplete,
} from "antd";
import {
  GlobalOutlined,
  TeamOutlined,
  EyeOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import useSelect from "../../hooks/useSelect";
import {
  getCameraLikeCameraNo
} from "../../service/deviceCamera";
const { RangePicker } = DatePicker;
const { Sider, Content } = Layout;
import { getevent, clearEvent } from "../../utils/loginLogger";
import { useLocation, useNavigate } from "react-router-dom";
import dayjs from "dayjs";
import { debounce } from "lodash";

const SensingEvent = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [tableData, setTableData] = useState([]);
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useState({
    page: 0,
    size: 10,
    cameraNo: location.state?.cameraNo ?? "",
    startTime: location.state?.startTime ?? "",
    endTime: location.state?.endTime ?? "",
    isNotGroup:false,
    type: "", // 0手动触发，1自动触发
    groupId: "", // 地理位置分组ID
    orgId: "", // 客户分组ID
    // 尝试多种后端排序参数格式
    sort: "foundTime,desc", // Spring Boot标准格式
    sortBy: "foundTime", // 排序字段
    sortOrder: "desc", // 排序方向
    orderBy: "foundTime desc", // 另一种格式
    ...(location.state?.ratEventId
      ? { ratEventId: location.state.ratEventId }
      : {}),
  });
  const [total, setTotal] = useState(0); // 总条数
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailData, setDetailData] = useState(null);
  const [activeTab, setActiveTab] = useState("location"); // 当前激活的分组类型
  const [selectedKeys, setSelectedKeys] = useState([]); // 选中的树节点
  const [expandedKeys, setExpandedKeys] = useState([]); // 展开的树节点
  const [treeData, setTreeData] = useState(null); // 地理位置树数据
  const [
    node,
    searchValue,
    autoExpandParent,
    filteredTreeData,
    setAutoExpandParent,
  ] = useSelect(treeData || [], setExpandedKeys);
  const [deviceData, setDeviceData] = useState([]); // 驱鼠器数据
  const [deviceOpen, setDeviceOpen] = useState(false); // 驱鼠器抽屉
  const [loading, setLoading] = useState(false); // 加载状态
  const [timeRange, setTimeRange] = useState("today"); // 时间范围选择
  const [dateRangeValue, setDateRangeValue] = useState(null); // 自定义时间范围值
  const [cameraOptions, setCameraOptions] = useState([]); // 摄像头编号自动完成选项

  // 获取地理位置树数据
  const fetchTreeData = async () => {
    setLoading(true);
    try {
      const response = await getDeviceGroupsByLocation(1);
      console.log("原始数据response:", response);
      if (response && response.length > 0) {
        // 转换数据格式，确保字段映射正确
        const formatTreeData = (nodes, level = 0) => {
          console.log(`第${level}层数据:`, nodes);
          return nodes.map((node) => {
            // 基础节点信息
            const formattedNode = {
              // 标题字段映射 - 优先使用groupName，如果没有则使用description
              groupName:
                node.groupName ||
                node.description ||
                node.name ||
                `未命名-${node.id}`,
              // ID字段映射
              id: node.id || node.locationId || node.key,
              // 保留原始数据
              ...node,
              // 初始化children为undefined
              children: undefined,
            };

            // 处理子节点 - 检查多种可能的子节点字段
            let childrenData = null;

            // 1. 首先检查children字段
            if (
              node.children &&
              Array.isArray(node.children) &&
              node.children.length > 0
            ) {
              childrenData = node.children;
              console.log(
                `节点 ${node.groupName} 有children:`,
                node.children.length
              );
            }
            // 2. 如果children为空，检查deviceLocationTreeVos字段
            else if (
              node.deviceLocationTreeVos &&
              Array.isArray(node.deviceLocationTreeVos) &&
              node.deviceLocationTreeVos.length > 0
            ) {
              childrenData = node.deviceLocationTreeVos;
              console.log(
                `节点 ${node.groupName} 有deviceLocationTreeVos:`,
                node.deviceLocationTreeVos.length,
                node.deviceLocationTreeVos
              );
            }

            // 如果有子节点数据，递归处理
            if (childrenData) {
              formattedNode.children = formatTreeData(childrenData, level + 1);
              console.log(
                `节点 ${formattedNode.groupName} 格式化后的子节点数量:`,
                formattedNode.children.length
              );
            }

            return formattedNode;
          });
        };

        const formattedData = formatTreeData(response);
        console.log("格式化后的完整数据:", formattedData);

        // 添加未分组选项
        const finalTreeData = [
          ...formattedData,
          { groupName: "未分组", id: "isNotGroup", isNotGroup: true, isLocal: 0 },
        ];

        setTreeData(finalTreeData);

        // 设置默认选中第一条数据
        setSelectedKeys([formattedData[0]?.id || "isNotGroup"]);

        // 收集所有节点的key用于展开
        const getAllKeys = (nodes) => {
          let keys = [];
          nodes.forEach((node) => {
            keys.push(node.id);
            if (node.children && node.children.length > 0) {
              keys = keys.concat(getAllKeys(node.children));
            }
          });
          return keys;
        };

        // 默认展开所有节点以便查看完整结构
        const allKeys = getAllKeys(finalTreeData);
        console.log("要展开的所有节点keys:", allKeys);
        setExpandedKeys(allKeys);

        // 触发默认选择的数据加载
        if (formattedData[0]?.id) {
          const defaultParams = {
            ...searchParams,
            page: 0,
            groupId: formattedData[0].id,
            orgId: "",
            isNotGroup: false,
          };
          setSearchParams(defaultParams);
          getEventData(defaultParams);
        }
      }
      setLoading(false);
    } catch (error) {
      console.error("获取地理位置树数据失败:", error);
    }
  };

  // 获取客户分组树
  const fetchCustomerGroupsTree = async () => {
    setLoading(true);
    try {
      const res = await getCustomerGroupsTree();
      const transformNode = (item) => ({
        groupName: item.ownershipName,
        id: item.ownershipId,
        children:
          item.children && item.children.length > 0
            ? item.children.map((child) => transformNode(child))
            : undefined,
      });
      const transformedData = res.map((item) => transformNode(item));

      // 添加未分组选项
      const finalTreeData = [
        ...transformedData,
        { groupName: "未分组", id: "isNotGroup", isNotGroup: true, isLocal: 0 },
      ];

      setTreeData(finalTreeData);
      setSelectedKeys([transformedData[0]?.id || "isNotGroup"]); // 设置当前选中的

      // 收集所有节点的key用于展开
      const getAllKeys = (nodes) => {
        let keys = [];
        nodes.forEach((node) => {
          keys.push(node.id);
          if (node.children && node.children.length > 0) {
            keys = keys.concat(getAllKeys(node.children));
          }
        });
        return keys;
      };

      const allKeys = getAllKeys(finalTreeData);
      setExpandedKeys(allKeys);

      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch customer groups tree:", error);
      setLoading(false);
    }
  };

  // 切换tab
  const onGroupTypeChange = async (key) => {
    if (window.cancelToken) {
      window.cancelToken(); // 取消请求
    }
    setTreeData(null);
    setActiveTab(key);
    if (key === "location") {
      await fetchTreeData();
    } else if (key === "customer") {
      await fetchCustomerGroupsTree();
    }
  };

  // 树节点选中
  const onTreeSelect = (selectedKeys, info) => {
    if (window.cancelToken) {
      window.cancelToken();
    }
    if (selectedKeys.length > 0) {
      setSelectedKeys(selectedKeys);

      // 根据选中的节点和当前tab更新搜索参数并获取数据
      const selectedGroupId = selectedKeys[0];
      const selectedNode = info.node;

      let params = {
        ...searchParams,
        page: 0, // 重置到第一页
      };

      // 检查是否选择的是"未分组"节点
      if (selectedNode.isNotGroup) {
        // 选择未分组时，设置isNotGroup为true，清除分组ID
        params.isNotGroup = true;
        params.groupId = "";
        params.orgId = "";
      } else {
        // 选择具体分组时，设置isNotGroup为false
        params.isNotGroup = false;

        if (activeTab === "location") {
          // 地理位置分组
          params.groupId = selectedGroupId;
          params.orgId = ""; // 清除客户分组ID
        } else if (activeTab === "customer") {
          // 客户分组
          params.orgId = selectedGroupId;
          params.groupId = ""; // 清除地理位置分组ID
        }
      }

      setSearchParams(params);
      getEventData(params);

      console.log("选中的节点:", selectedKeys, info);
      console.log("选中节点是否为未分组:", selectedNode.isNotGroup);
      console.log("更新的搜索参数:", params);
    } else {
      // 如果没有选中任何节点，清除相应的筛选
      setSelectedKeys([]);
      let params = {
        ...searchParams,
        page: 0,
        isNotGroup: false, // 重置isNotGroup
      };

      if (activeTab === "location") {
        params.groupId = ""; // 清除地理位置分组ID
      } else if (activeTab === "customer") {
        params.orgId = ""; // 清除客户分组ID
      }

      setSearchParams(params);
      getEventData(params);
    }
  };

  useEffect(() => {
    // 获取地理位置树数据
    fetchTreeData();
  }, []);

  useEffect(() => {
    // 获取localStorage中保存的鼠患事件ID
    const ratEventId = getevent();
    if (ratEventId) {
      // 如果存在鼠患事件ID，则设置到searchParams并调用getUserPage接口
      const params = {
        ...searchParams,
        ratEventId,
      };
      setSearchParams(params);
      getEventData(params);
      clearEvent();
    } else {
      // 否则调用普通获取事件数据接口
      getEventData(searchParams);

      form.setFieldsValue({
        cameraNo: location.state?.cameraNo,
      });

      // 如果有传入的时间范围，设置为自定义模式
      if (location.state?.startTime && location.state?.endTime) {
        setTimeRange("custom");
        setDateRangeValue([
          dayjs(location.state.startTime),
          dayjs(location.state.endTime),
        ]);
      } else {
        // 默认设置为今天
        handleTimeRangeChange("today");
      }
      navigate(".", { replace: true, state: null });
    }

    // 清理函数，在组件卸载时取消请求
    return () => {
      if (window.cancelToken) {
        window.cancelToken(); // 取消请求
      }
    };
  }, []);

  const getEventData = useCallback(async (params) => {
    setLoading(true);
    try {
      console.log("实际发送的请求参数:", params);
      console.log("当前searchParams状态:", searchParams);
      let res = await getSensingEventList(params);
      console.log("API返回的数据:", res);

      // 如果后端不支持排序，在前端进行排序
      let sortedData = res.content;
      if (sortedData && sortedData.length > 0) {
        console.log("排序前的第一条数据:", sortedData[0]);
        console.log("排序前的最后一条数据:", sortedData[sortedData.length - 1]);

        sortedData = [...res.content].sort((a, b) => {
          // 按发现时间倒序排列
          const timeA = new Date(a.foundTime);
          const timeB = new Date(b.foundTime);

          // 检查日期是否有效
          if (isNaN(timeA.getTime()) || isNaN(timeB.getTime())) {
            console.warn("日期格式无效:", a.foundTime, b.foundTime);
            // 如果日期无效，按字符串比较
            return b.foundTime > a.foundTime ? 1 : -1;
          }

          return timeB - timeA; // 倒序：新的在前
        });

        console.log("排序后的第一条数据:", sortedData[0]);
        console.log("排序后的最后一条数据:", sortedData[sortedData.length - 1]);
        console.log("前端排序后的数据:", sortedData);
      }

      setTableData(sortedData);
      setTotal(res.totalElements);
      setLoading(false);
    } catch (err) {
      console.log("获取数据失败:", err);
    }
  }, []);

  const handleSearch = async () => {
    const values = await form.validateFields();
    const { ratEventId, ...restSearchParams } = searchParams;
    const params = {
      ...restSearchParams,
      page: 0,
      cameraNo: values.cameraNo,
      type: values.type,
      startTime: dateRangeValue?.[0]?.format("YYYY-MM-DD HH:mm:ss"),
      endTime: dateRangeValue?.[1]?.format("YYYY-MM-DD HH:mm:ss"),
    };
    setSearchParams(params);
    getEventData(params);
  };

  const handleReset = () => {
    form.resetFields();
    // 重置时清除地理位置选择
    setSelectedKeys([]);
    // 重置时间选择器状态
    setTimeRange("today");
    setDateRangeValue(null);

    const params = {
      page: 0,
      size: 10,
      cameraNo: "",
      startTime: "",
      endTime: "",
      type: "",
      groupId: "", // 清除地理位置分组ID
      // 保持排序参数
      sort: "foundTime,desc",
      sortBy: "foundTime",
      sortOrder: "desc",
      orderBy: "foundTime desc",
    };
    setSearchParams(params);
    getEventData(params);
  };

  const handleTableChange = (pagination) => {
    const params = {
      ...searchParams,
      page: pagination.current - 1,
      size: pagination.pageSize,
    };
    setSearchParams(params);
    getEventData(params);
  };

  const handleDelete = async (record) => {
    Modal.confirm({
      title: "确认删除",
      content: "是否确认删除这条驱鼠记录？",
      okText: "是",
      cancelText: "否",
      centered: true,
      onOk: async () => {
        try {
          await deleteSensingEvent(record.id);
          getEventData(searchParams);
        } catch (err) {
          console.log(err);
        }
      },
    });
  };

  const handleView = async (record) => {
    try {
      const detail = await getSensingEvent(record.id);
      setDetailData(detail);
      setDetailVisible(true);
    } catch (err) {
      console.log(err);
    }
  };

  const handleDevice = async (record) => {
    try {
      const cameraId = record.cid;
      const res = await getDevicesByCameraId(cameraId);
      setDeviceData(res);
      setDeviceOpen(true);
    } catch (error) {
      console.log(error);
    }
  };

  // 处理时间范围选择变化
  const handleTimeRangeChange = (value) => {
    setTimeRange(value);

    if (value !== "custom") {
      // 根据选择的时间范围设置对应的时间值
      const now = dayjs();
      let startTime, endTime;

      switch (value) {
        case "today":
          startTime = now.startOf("day");
          endTime = now.endOf("day");
          break;
        case "yesterday":
          startTime = now.subtract(1, "day").startOf("day");
          endTime = now.subtract(1, "day").endOf("day");
          break;
        case "week":
          startTime = now.startOf("week");
          endTime = now.endOf("week");
          break;
        case "month":
          startTime = now.startOf("month");
          endTime = now.endOf("month");
          break;
        default:
          startTime = null;
          endTime = null;
      }

      if (startTime && endTime) {
        setDateRangeValue([startTime, endTime]);
        form.setFieldsValue({
          timeRange: [startTime, endTime],
        });
      }
    }
  };

  // 处理自定义时间范围变化
  const handleCustomTimeRangeChange = (dates) => {
    setDateRangeValue(dates);
    form.setFieldsValue({
      timeRange: dates,
    });
  };

  // 搜索摄像头编号
  const handleCameraNoSearch = debounce(async (value) => {
    if (value && value.trim()) {
      try {
        const response = await getCameraLikeCameraNo(value.trim());
        if (response && Array.isArray(response)) {
          // 将返回的字符串数组转换为AutoComplete需要的格式
          setCameraOptions(
            response.map((cameraNo) => ({
              value: cameraNo,
              label: cameraNo,
            }))
          );
        } else {
          setCameraOptions([]);
        }
      } catch (error) {
        console.error("搜索摄像头编号失败:", error);
        setCameraOptions([]);
      }
    } else {
      setCameraOptions([]);
    }
  }, 300); // 300ms防抖

  let columns = [
    {
      title: "摄像头编号",
      dataIndex: "cameraNo",
      key: "cameraNo",
    },
    {
      title: "摄像头名称",
      dataIndex: "cameraName",
      key: "cameraName",
    },
    {
      title: "触发方式",
      dataIndex: "type",
      key: "type",
      render: (type) => {
        return type === 0 ? "手动触发" : type === 1 ? "自动触发" : null;
      },
    },
    {
      title: "发现时间",
      dataIndex: "foundTime",
      key: "foundTime",
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      width: "10%",
      render: (text, record) => (
        <Space>
          <Tooltip title="详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="驱鼠记录">
            <Button
              type="link"
              icon={<FileTextOutlined />}
              onClick={() => handleDevice(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="sensing-event">
      <div className="contentBox">
        <Row gutter={16} style={{ height: "calc(100vh - 400px)" }}>
          {/* 左侧地理位置树 */}
          <Col span={6}>
            <Card
              size="small"
              className="location-tree-card tree-card"
              style={{
                height: "111%",
                borderRadius: "8px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
              }}
              styles={{
                body: {
                  padding: "0",
                  height: "calc(100% - 57px)",
                  overflow: "hidden",
                },
              }}
            >
              <Tabs
                activeKey={activeTab}
                onChange={onGroupTypeChange}
                style={{ height: "100%" }}
                items={[
                  {
                    key: "location",
                    label: (
                      <span>
                        <GlobalOutlined />
                        地理分组
                      </span>
                    ),
                    children: (
                      <div
                        style={{
                          padding: "12px",
                          height: "calc(100vh - 400px)",
                          overflow: "auto",
                        }}
                      >
                        {node()}
                        {treeData && (
                          <Tree
                            showIcon
                            onExpand={(keys) => {
                              setExpandedKeys(keys);
                              setAutoExpandParent(false); // 用户手动展开后关闭自动展开
                            }}
                            autoExpandParent={autoExpandParent}
                            onSelect={onTreeSelect}
                            selectedKeys={selectedKeys}
                            expandedKeys={expandedKeys}
                            treeData={searchValue ? filteredTreeData : treeData}
                            blockNode
                            fieldNames={{
                              title: "groupName",
                              key: "id",
                              children: "children",
                            }}
                          />
                        )}
                      </div>
                    ),
                  },
                  {
                    key: "customer",
                    label: (
                      <span>
                        <TeamOutlined />
                        客户分组
                      </span>
                    ),
                    children: (
                      <div
                        style={{
                          padding: "12px",
                          height: "calc(100vh - 400px)",
                          overflow: "auto",
                        }}
                      >
                        {node()}
                        {treeData && (
                          <Tree
                            showIcon
                            onExpand={(keys) => {
                              setExpandedKeys(keys);
                              setAutoExpandParent(false); // 用户手动展开后关闭自动展开
                            }}
                            autoExpandParent={autoExpandParent}
                            onSelect={onTreeSelect}
                            selectedKeys={selectedKeys}
                            expandedKeys={expandedKeys}
                            treeData={searchValue ? filteredTreeData : treeData}
                            blockNode
                            fieldNames={{
                              title: "groupName",
                              key: "id",
                              children: "children",
                            }}
                          />
                        )}
                      </div>
                    ),
                  },
                ]}
              />
            </Card>
          </Col>

          {/* 右侧表格 */}
          <Col span={18}>
            <div className="searchBox">
              <Form form={form} layout="inline">
                <Form.Item name="cameraNo" label="摄像头编号：">
                  <AutoComplete
                    placeholder="请输入摄像头编号"
                    options={cameraOptions}
                    onSearch={handleCameraNoSearch}
                    onSelect={(value) => {
                      form.setFieldsValue({ cameraNo: value });
                    }}
                    filterOption={false}
                    style={{ width: 200 }}
                  />
                </Form.Item>
                <Form.Item name="type" label="触发方式">
                  <Select
                    placeholder="请选择触发方式"
                    style={{ width: 120 }}
                    options={[
                      { value: 0, label: "手动触发" },
                      { value: 1, label: "自动触发" },
                    ]}
                  />
                </Form.Item>
                <Form.Item>
                  <Space size="middle" className="search-right">
                    <Select
                      value={timeRange}
                      style={{ width: 120 }}
                      options={[
                        { value: "today", label: "今天" },
                        { value: "yesterday", label: "昨天" },
                        { value: "week", label: "本周" },
                        { value: "month", label: "本月" },
                        { value: "custom", label: "自定义" },
                      ]}
                      onChange={handleTimeRangeChange}
                    />
                    <RangePicker
                      value={dateRangeValue}
                      onChange={handleCustomTimeRangeChange}
                      showTime
                      format="YYYY-MM-DD HH:mm:ss"
                      disabled={timeRange !== "custom"}
                    />
                  </Space>
                </Form.Item>
                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    onClick={handleSearch}
                    style={{
                      marginRight: 8,
                    }}
                  >
                    搜索
                  </Button>
                  <Button onClick={handleReset}>重置</Button>
                </Form.Item>
              </Form>
            </div>
            <Card
              title="📋 驱鼠记录列表"
              size="small"
              className="table-card"
              style={{
                height: "100%",
                borderRadius: "8px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
              }}
              styles={{
                body: {
                  padding: "0",
                  height: "calc(100% - 57px)",
                  overflow: "hidden",
                },
              }}
            >
              <div className="tableBox" style={{ height: "100%" }}>
                <Table
                  columns={columns}
                  loading={loading}
                  dataSource={tableData}
                  pagination={{
                    total: total,
                    current: searchParams.page + 1,
                    pageSize: searchParams.size,
                    showTotal: (total) => `共 ${total} 条`,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    size: "small",
                  }}
                  onChange={handleTableChange}
                  rowKey={"id"}
                  size="small"
                  scroll={{ y: "calc(100vh - 350px)" }}
                />
              </div>
            </Card>
          </Col>
        </Row>
      </div>
      <Drawer
        title={
          <div style={{
            display: 'flex',
            alignItems: 'center',
            fontSize: '18px',
            fontWeight: 600,
            color: '#1890ff'
          }}>
            <EyeOutlined style={{ marginRight: '8px' }} />
            驱鼠记录详情
          </div>
        }
        open={detailVisible}
        onClose={() => setDetailVisible(false)}
        width={600}
        placement="right"
        styles={{
          body: {
            padding: '24px',
            background: '#fafafa'
          }
        }}
      >
        {detailData && (
          <Card
            style={{
              borderRadius: '12px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              border: 'none'
            }}
            styles={{
              body: {
                padding: '24px'
              }
            }}
          >
            <Descriptions
              column={1}
              labelStyle={{
                fontWeight: 600,
                color: '#666',
                width: '120px'
              }}
              contentStyle={{
                color: '#333',
                fontWeight: 500
              }}
            >
              <Descriptions.Item
                label={
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    📹 摄像头编号
                  </span>
                }
              >
                <Tag color="blue" style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {detailData.cameraNo}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    📝 摄像头名称
                  </span>
                }
              >
                <span style={{ fontSize: '14px' }}>{detailData.cameraName}</span>
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    ⚡ 触发方式
                  </span>
                }
              >
                <Tag
                  color={detailData.type === 0 ? 'orange' : 'green'}
                  style={{ fontSize: '14px', padding: '4px 12px' }}
                >
                  {detailData.type === 0
                    ? "手动触发"
                    : detailData.type === 1
                    ? "自动触发"
                    : "未知"}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    🕒 发现时间
                  </span>
                }
              >
                <Tag color="purple" style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {detailData.foundTime}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        )}
      </Drawer>
      <Modal
        title="驱鼠器列表"
        open={deviceOpen}
        onCancel={() => setDeviceOpen(false)}
        footer={null}
        width={800}
        centered
      >
        <List
          dataSource={deviceData}
          renderItem={(item) => (
            <List.Item style={{ border: "none", padding: "8px 0" }}>
              <div style={{ width: "100%" }}>
                <div style={{ marginBottom: "4px" }}>
                  <strong>设备名称：</strong>
                  {item.deviceName || "-"}
                </div>
                <div>
                  <strong>设备编号：</strong>
                  {item.deviceNo || "-"}
                </div>
              </div>
            </List.Item>
          )}
          style={{ border: "none" }}
        />
      </Modal>
    </div>
  );
};

export default SensingEvent;
