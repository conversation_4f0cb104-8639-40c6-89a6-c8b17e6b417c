# 摄像头编号自动完成功能测试指南

## 测试步骤

### 1. 访问页面
1. 启动项目：`npm run dev`
2. 打开浏览器访问：`http://localhost:5174/`
3. 导航到驱鼠记录页面（SensingEvent）

### 2. 测试自动完成功能

#### 测试用例1：基本搜索功能
1. 在摄像头编号输入框中输入 "3TPC"
2. 等待约300ms（防抖时间）
3. 验证是否出现下拉列表
4. 验证下拉列表中是否包含匹配的摄像头编号

#### 测试用例2：选择功能
1. 在输入框中输入部分摄像头编号
2. 等待下拉列表出现
3. 点击下拉列表中的任意一个选项
4. 验证选中的值是否正确填充到输入框中

#### 测试用例3：防抖功能
1. 快速连续输入多个字符
2. 验证接口调用是否被正确防抖（不会频繁调用）
3. 只有在停止输入300ms后才会调用接口

#### 测试用例4：空值处理
1. 清空输入框
2. 验证下拉列表是否消失
3. 验证不会调用接口

#### 测试用例5：错误处理
1. 断开网络连接
2. 在输入框中输入内容
3. 验证错误是否被正确处理，不会影响页面功能

## 预期结果

### 正常情况下的表现
- 输入内容后300ms内出现下拉列表
- 下拉列表显示匹配的摄像头编号
- 点击选项后正确填充到输入框
- 样式与原有设计保持一致

### 异常情况下的表现
- 网络错误时不会影响页面正常使用
- 空输入时不会调用接口
- 接口返回空数据时下拉列表为空

## 调试信息

### 浏览器控制台
- 可以在控制台中查看接口调用情况
- 错误信息会在控制台中显示
- 可以查看 `response` 数据格式

### 网络面板
- 可以在Network面板中查看接口调用
- 验证防抖是否生效
- 检查接口参数和返回值

## 常见问题排查

### 1. 下拉列表不出现
- 检查接口是否正常返回数据
- 检查 `cameraOptions` 状态是否正确更新
- 验证 `handleCameraNoSearch` 函数是否被调用

### 2. 选择后不填充
- 检查 `onSelect` 回调函数
- 验证 `form.setFieldsValue` 是否正确调用

### 3. 接口调用频繁
- 检查防抖设置是否正确
- 验证防抖时间是否合适

### 4. 样式问题
- 检查 AutoComplete 组件的 style 属性
- 验证 Ant Design 版本兼容性
